export { a as addDependency, j as addDependencyCommand, b as addDevDependency, c as dedupeDependencies, d as detectPackageManager, g as dlx, l as dlxCommand, e as ensureDependencyInstalled, i as installDependencies, h as installDependenciesCommand, p as packageManagers, r as removeDependency, f as runScript, k as runScriptCommand } from './shared/nypm.CLjaS_sz.mjs';
import 'pkg-types';
import 'node:module';
import 'pathe';
import 'tinyexec';
import 'node:fs';
import 'node:fs/promises';
