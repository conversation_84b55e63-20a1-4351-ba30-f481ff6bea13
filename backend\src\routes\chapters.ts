import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { ChapterService } from '../services/chapterService';

const router = Router();
const chapterService = new ChapterService();

// 获取章节列表
router.get('/', asyncHandler(async (req, res) => {
  const { novelId, page = 1, limit = 20 } = req.query;

  if (!novelId) {
    return res.status(400).json({
      success: false,
      error: 'Novel ID is required'
    });
  }

  const result = await chapterService.getChapters(
    novelId as string,
    parseInt(page as string),
    parseInt(limit as string)
  );

  res.json({
    success: true,
    data: result
  });
}));

// 获取单个章节
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const chapter = await chapterService.getChapterById(id);

  if (!chapter) {
    return res.status(404).json({
      success: false,
      error: 'Chapter not found'
    });
  }

  res.json({
    success: true,
    data: chapter
  });
}));

// 修改章节
router.post('/:id/modify', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { rules } = req.body;

  if (!rules || !Array.isArray(rules)) {
    return res.status(400).json({
      success: false,
      error: 'Modification rules are required'
    });
  }

  const result = await chapterService.modifyChapter(id, rules);

  res.json({
    success: true,
    data: result
  });
}));

// 批量修改章节
router.post('/batch-modify', asyncHandler(async (req, res) => {
  const { chapterIds, rules } = req.body;

  if (!chapterIds || !Array.isArray(chapterIds) || !rules || !Array.isArray(rules)) {
    return res.status(400).json({
      success: false,
      error: 'Chapter IDs and modification rules are required'
    });
  }

  const result = await chapterService.batchModifyChapters(chapterIds, rules);

  res.json({
    success: true,
    data: result
  });
}));

// 获取章节修改历史
router.get('/:id/history', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const history = await chapterService.getChapterHistory(id);

  res.json({
    success: true,
    data: history
  });
}));

// 恢复章节到原始版本
router.post('/:id/restore', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const result = await chapterService.restoreChapter(id);

  res.json({
    success: true,
    data: result
  });
}));

export default router;
