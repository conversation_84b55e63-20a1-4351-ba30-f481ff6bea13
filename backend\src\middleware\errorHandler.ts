import { Request, Response, NextFunction } from 'express';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export const errorHandler = (
  err: AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';

  // 记录错误
  console.error(`Error ${statusCode}: ${message}`);
  console.error(err.stack);

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    res.status(statusCode).json({
      error: {
        message,
        statusCode,
        stack: err.stack,
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method
      }
    });
  } else {
    // 生产环境只返回基本错误信息
    res.status(statusCode).json({
      error: {
        message: statusCode === 500 ? 'Internal Server Error' : message,
        statusCode,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const createError = (message: string, statusCode: number = 500): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
