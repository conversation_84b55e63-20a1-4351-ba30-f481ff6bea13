import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { NovelService } from '../services/novelService';

const router = Router();
const novelService = new NovelService();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/plain' || path.extname(file.originalname) === '.txt') {
      cb(null, true);
    } else {
      cb(new Error('Only .txt files are allowed'));
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  }
});

// 上传并导入小说
router.post('/upload', upload.single('novel'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded'
    });
  }

  const { projectId, title, author } = req.body;

  if (!projectId) {
    return res.status(400).json({
      success: false,
      error: 'Project ID is required'
    });
  }

  const result = await novelService.importNovel({
    projectId,
    title: title || path.basename(req.file.originalname, '.txt'),
    author,
    filePath: req.file.path,
    originalName: req.file.originalname
  });

  res.status(201).json({
    success: true,
    data: result
  });
}));

// 获取小说列表
router.get('/', asyncHandler(async (req, res) => {
  const { projectId } = req.query;

  const novels = await novelService.getNovels(projectId as string);

  res.json({
    success: true,
    data: novels
  });
}));

// 获取单个小说详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const novel = await novelService.getNovelById(id);

  if (!novel) {
    return res.status(404).json({
      success: false,
      error: 'Novel not found'
    });
  }

  res.json({
    success: true,
    data: novel
  });
}));

// 分析小说结构
router.post('/:id/analyze', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const result = await novelService.analyzeNovel(id);

  res.json({
    success: true,
    data: result
  });
}));

// 获取小说分析结果
router.get('/:id/analysis', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const analysis = await novelService.getNovelAnalysis(id);

  res.json({
    success: true,
    data: analysis
  });
}));

export default router;
