import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AIService } from '../services/aiService';

const router = Router();
const aiService = new AIService();

// 测试 AI 连接
router.get('/test', asyncHandler(async (req, res) => {
  const result = await aiService.testConnection();

  res.json({
    success: true,
    data: result
  });
}));

// 分析文本
router.post('/analyze', asyncHandler(async (req, res) => {
  const { text, type = 'general' } = req.body;

  if (!text) {
    return res.status(400).json({
      success: false,
      error: 'Text is required'
    });
  }

  const result = await aiService.analyzeText(text, type);

  res.json({
    success: true,
    data: result
  });
}));

// 重写文本
router.post('/rewrite', asyncHandler(async (req, res) => {
  const { text, rules, context } = req.body;

  if (!text) {
    return res.status(400).json({
      success: false,
      error: 'Text is required'
    });
  }

  const result = await aiService.rewriteText(text, rules, context);

  res.json({
    success: true,
    data: result
  });
}));

// 提取角色信息
router.post('/extract-characters', asyncHandler(async (req, res) => {
  const { text } = req.body;

  if (!text) {
    return res.status(400).json({
      success: false,
      error: 'Text is required'
    });
  }

  const result = await aiService.extractCharacters(text);

  res.json({
    success: true,
    data: result
  });
}));

// 分析情节线
router.post('/analyze-plot', asyncHandler(async (req, res) => {
  const { text } = req.body;

  if (!text) {
    return res.status(400).json({
      success: false,
      error: 'Text is required'
    });
  }

  const result = await aiService.analyzePlot(text);

  res.json({
    success: true,
    data: result
  });
}));

// 生成摘要
router.post('/summarize', asyncHandler(async (req, res) => {
  const { text, length = 'medium' } = req.body;

  if (!text) {
    return res.status(400).json({
      success: false,
      error: 'Text is required'
    });
  }

  const result = await aiService.generateSummary(text, length);

  res.json({
    success: true,
    data: result
  });
}));

export default router;
