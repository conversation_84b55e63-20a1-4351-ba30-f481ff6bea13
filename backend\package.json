{"name": "backend", "module": "src/server.ts", "type": "commonjs", "private": true, "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5.9.2"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@prisma/client": "^6.16.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^4.0.0", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.5.2", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemon": "^3.1.10", "prisma": "^6.16.2", "ts-node": "^10.9.2", "zod": "^4.1.11"}}