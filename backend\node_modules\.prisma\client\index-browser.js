
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.16.2
 * Query Engine version: 1c57fdcd7e44b29b9313256c76699e91c3ac3c43
 */
Prisma.prismaVersion = {
  client: "6.16.2",
  engine: "1c57fdcd7e44b29b9313256c76699e91c3ac3c43"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  settings: 'settings'
};

exports.Prisma.NovelScalarFieldEnum = {
  id: 'id',
  title: 'title',
  author: 'author',
  summary: 'summary',
  originalPath: 'originalPath',
  totalChapters: 'totalChapters',
  totalWords: 'totalWords',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  projectId: 'projectId'
};

exports.Prisma.ChapterScalarFieldEnum = {
  id: 'id',
  number: 'number',
  title: 'title',
  originalContent: 'originalContent',
  modifiedContent: 'modifiedContent',
  wordCount: 'wordCount',
  analysis: 'analysis',
  status: 'status',
  modifiedAt: 'modifiedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  novelId: 'novelId'
};

exports.Prisma.CharacterScalarFieldEnum = {
  id: 'id',
  name: 'name',
  aliases: 'aliases',
  description: 'description',
  personality: 'personality',
  background: 'background',
  firstAppearance: 'firstAppearance',
  importanceScore: 'importanceScore',
  characterArc: 'characterArc',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  novelId: 'novelId'
};

exports.Prisma.RelationshipScalarFieldEnum = {
  id: 'id',
  type: 'type',
  description: 'description',
  development: 'development',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  fromCharacterId: 'fromCharacterId',
  toCharacterId: 'toCharacterId'
};

exports.Prisma.PlotLineScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  description: 'description',
  chapters: 'chapters',
  keyEvents: 'keyEvents',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  novelId: 'novelId'
};

exports.Prisma.ChapterCharacterScalarFieldEnum = {
  id: 'id',
  importance: 'importance',
  chapterId: 'chapterId',
  characterId: 'characterId'
};

exports.Prisma.ModifyRuleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  config: 'config',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  projectId: 'projectId'
};

exports.Prisma.NovelKnowledgeBaseScalarFieldEnum = {
  id: 'id',
  worldBuilding: 'worldBuilding',
  themes: 'themes',
  writingStyle: 'writingStyle',
  vectorIndexPath: 'vectorIndexPath',
  lastAnalyzed: 'lastAnalyzed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  novelId: 'novelId'
};

exports.Prisma.ProcessingTaskScalarFieldEnum = {
  id: 'id',
  type: 'type',
  status: 'status',
  progress: 'progress',
  input: 'input',
  output: 'output',
  error: 'error',
  novelId: 'novelId',
  chapterId: 'chapterId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  completedAt: 'completedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.NovelStatus = exports.$Enums.NovelStatus = {
  IMPORTED: 'IMPORTED',
  ANALYZING: 'ANALYZING',
  ANALYZED: 'ANALYZED',
  MODIFYING: 'MODIFYING',
  COMPLETED: 'COMPLETED',
  ERROR: 'ERROR'
};

exports.ChapterStatus = exports.$Enums.ChapterStatus = {
  ORIGINAL: 'ORIGINAL',
  ANALYZING: 'ANALYZING',
  ANALYZED: 'ANALYZED',
  MODIFYING: 'MODIFYING',
  MODIFIED: 'MODIFIED',
  ERROR: 'ERROR'
};

exports.RelationType = exports.$Enums.RelationType = {
  ROMANTIC: 'ROMANTIC',
  FAMILY: 'FAMILY',
  FRIEND: 'FRIEND',
  ENEMY: 'ENEMY',
  MENTOR: 'MENTOR',
  COLLEAGUE: 'COLLEAGUE',
  OTHER: 'OTHER'
};

exports.PlotType = exports.$Enums.PlotType = {
  MAIN: 'MAIN',
  ROMANCE: 'ROMANCE',
  SIDE: 'SIDE',
  BACKGROUND: 'BACKGROUND'
};

exports.PlotStatus = exports.$Enums.PlotStatus = {
  ONGOING: 'ONGOING',
  RESOLVED: 'RESOLVED',
  ABANDONED: 'ABANDONED'
};

exports.RuleType = exports.$Enums.RuleType = {
  CONTENT_WEIGHT: 'CONTENT_WEIGHT',
  CHARACTER_ADJUSTMENT: 'CHARACTER_ADJUSTMENT',
  STYLE_CHANGE: 'STYLE_CHANGE',
  PLOT_FILTER: 'PLOT_FILTER'
};

exports.TaskType = exports.$Enums.TaskType = {
  NOVEL_ANALYSIS: 'NOVEL_ANALYSIS',
  CHAPTER_ANALYSIS: 'CHAPTER_ANALYSIS',
  CHAPTER_MODIFY: 'CHAPTER_MODIFY',
  CONSISTENCY_CHECK: 'CONSISTENCY_CHECK'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  PENDING: 'PENDING',
  RUNNING: 'RUNNING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  Project: 'Project',
  Novel: 'Novel',
  Chapter: 'Chapter',
  Character: 'Character',
  Relationship: 'Relationship',
  PlotLine: 'PlotLine',
  ChapterCharacter: 'ChapterCharacter',
  ModifyRule: 'ModifyRule',
  NovelKnowledgeBase: 'NovelKnowledgeBase',
  ProcessingTask: 'ProcessingTask'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
