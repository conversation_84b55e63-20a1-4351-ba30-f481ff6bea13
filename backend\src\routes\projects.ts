import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import prisma from '../config/database';

const router = Router();

// 获取所有项目
router.get('/', asyncHandler(async (req, res) => {
  const projects = await prisma.project.findMany({
    include: {
      novels: {
        select: {
          id: true,
          title: true,
          status: true,
          totalChapters: true,
          createdAt: true
        }
      },
      _count: {
        select: {
          novels: true,
          modifyRules: true
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });

  res.json({
    success: true,
    data: projects
  });
}));

// 获取单个项目
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await prisma.project.findUnique({
    where: { id },
    include: {
      novels: {
        include: {
          _count: {
            select: {
              chapters: true,
              characters: true
            }
          }
        }
      },
      modifyRules: true
    }
  });

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  res.json({
    success: true,
    data: project
  });
}));

// 创建新项目
router.post('/', asyncHandler(async (req, res) => {
  const { name, description, settings } = req.body;

  if (!name) {
    return res.status(400).json({
      success: false,
      error: 'Project name is required'
    });
  }

  const project = await prisma.project.create({
    data: {
      name,
      description,
      settings: settings || {}
    }
  });

  res.status(201).json({
    success: true,
    data: project
  });
}));

// 更新项目
router.put('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, description, settings } = req.body;

  const project = await prisma.project.update({
    where: { id },
    data: {
      name,
      description,
      settings
    }
  });

  res.json({
    success: true,
    data: project
  });
}));

// 删除项目
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  await prisma.project.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: 'Project deleted successfully'
  });
}));

export default router;
