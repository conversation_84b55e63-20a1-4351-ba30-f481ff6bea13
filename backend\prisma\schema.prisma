// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 项目表 - 每个小说对应一个项目
model Project {
  id          String   @id @default(cuid())
  name        String // 项目名称
  description String? // 项目描述
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 项目设置
  settings Json? // 存储项目级别的配置

  // 关联关系
  novels      Novel[]
  modifyRules ModifyRule[]

  @@map("projects")
}

// 小说表
model Novel {
  id            String      @id @default(cuid())
  title         String // 小说标题
  author        String? // 作者
  summary       String? // 简介
  originalPath  String // 原始文件路径
  totalChapters Int         @default(0) // 总章节数
  totalWords    Int         @default(0) // 总字数
  status        NovelStatus @default(IMPORTED) // 处理状态

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  chapters      Chapter[]
  characters    Character[]
  plotLines     PlotLine[]
  knowledgeBase NovelKnowledgeBase?

  @@map("novels")
}

// 小说状态枚举
enum NovelStatus {
  IMPORTED // 已导入
  ANALYZING // 分析中
  ANALYZED // 已分析
  MODIFYING // 修改中
  COMPLETED // 已完成
  ERROR // 错误状态
}

// 章节表
model Chapter {
  id              String  @id @default(cuid())
  number          Int // 章节号
  title           String // 章节标题
  originalContent String // 原始内容
  modifiedContent String? // 修改后内容
  wordCount       Int     @default(0) // 字数

  // 章节分析数据
  analysis Json? // 存储章节分析结果

  // 处理状态
  status     ChapterStatus @default(ORIGINAL)
  modifiedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  novelId String
  novel   Novel  @relation(fields: [novelId], references: [id], onDelete: Cascade)

  // 章节与角色的关系
  chapterCharacters ChapterCharacter[]

  @@unique([novelId, number])
  @@map("chapters")
}

// 章节状态枚举
enum ChapterStatus {
  ORIGINAL // 原始状态
  ANALYZING // 分析中
  ANALYZED // 已分析
  MODIFYING // 修改中
  MODIFIED // 已修改
  ERROR // 错误状态
}

// 角色表
model Character {
  id          String  @id @default(cuid())
  name        String // 角色名称
  aliases     String? // 别名列表 (JSON string)
  description String? // 外貌描述
  personality String? // 性格特征 (JSON string)
  background  String? // 背景故事

  // 角色数据
  firstAppearance Int? // 首次出现章节
  importanceScore Float   @default(0) // 重要性评分 (0-100)
  characterArc    String? // 角色发展弧线

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  novelId String
  novel   Novel  @relation(fields: [novelId], references: [id], onDelete: Cascade)

  // 角色关系
  relationshipsFrom Relationship[] @relation("CharacterFrom")
  relationshipsTo   Relationship[] @relation("CharacterTo")

  // 章节与角色的关系
  chapterCharacters ChapterCharacter[]

  @@map("characters")
}

// 角色关系表
model Relationship {
  id          String       @id @default(cuid())
  type        RelationType // 关系类型
  description String? // 关系描述
  development Json? // 关系发展历程

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  fromCharacterId String
  toCharacterId   String
  fromCharacter   Character @relation("CharacterFrom", fields: [fromCharacterId], references: [id], onDelete: Cascade)
  toCharacter     Character @relation("CharacterTo", fields: [toCharacterId], references: [id], onDelete: Cascade)

  @@unique([fromCharacterId, toCharacterId])
  @@map("relationships")
}

// 关系类型枚举
enum RelationType {
  ROMANTIC // 恋爱关系
  FAMILY // 家庭关系
  FRIEND // 朋友关系
  ENEMY // 敌对关系
  MENTOR // 师徒关系
  COLLEAGUE // 同事关系
  OTHER // 其他关系
}

// 情节线表
model PlotLine {
  id          String     @id @default(cuid())
  name        String // 情节线名称
  type        PlotType // 情节线类型
  description String? // 描述
  chapters    String // 涉及的章节号列表 (JSON string)
  keyEvents   Json? // 关键事件
  status      PlotStatus @default(ONGOING) // 解决状态

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  novelId String
  novel   Novel  @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@map("plot_lines")
}

// 情节线类型枚举
enum PlotType {
  MAIN // 主线
  ROMANCE // 感情线
  SIDE // 支线
  BACKGROUND // 背景线
}

// 情节线状态枚举
enum PlotStatus {
  ONGOING // 进行中
  RESOLVED // 已解决
  ABANDONED // 已放弃
}

// 章节-角色关联表
model ChapterCharacter {
  id         String @id @default(cuid())
  importance Float  @default(0) // 在该章节中的重要性

  // 关联关系
  chapterId   String
  characterId String
  chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)
  character   Character @relation(fields: [characterId], references: [id], onDelete: Cascade)

  @@unique([chapterId, characterId])
  @@map("chapter_characters")
}

// 修改规则表
model ModifyRule {
  id          String   @id @default(cuid())
  name        String // 规则名称
  description String? // 规则描述
  type        RuleType // 规则类型
  config      Json // 规则配置
  isActive    Boolean  @default(true) // 是否激活

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("modify_rules")
}

// 规则类型枚举
enum RuleType {
  CONTENT_WEIGHT // 内容权重调整
  CHARACTER_ADJUSTMENT // 角色性格修正
  STYLE_CHANGE // 文风调整
  PLOT_FILTER // 情节过滤
}

// 小说知识库表
model NovelKnowledgeBase {
  id String @id @default(cuid())

  // 结构化数据
  worldBuilding Json? // 世界观设定
  themes        String? // 主题 (JSON string)
  writingStyle  Json? // 写作风格分析

  // 向量索引相关
  vectorIndexPath String? // 向量索引文件路径
  lastAnalyzed    DateTime? // 最后分析时间

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  novelId String @unique
  novel   Novel  @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@map("novel_knowledge_bases")
}

// 处理任务表 - 用于跟踪异步任务
model ProcessingTask {
  id       String     @id @default(cuid())
  type     TaskType // 任务类型
  status   TaskStatus @default(PENDING) // 任务状态
  progress Float      @default(0) // 进度 (0-100)

  // 任务数据
  input  Json? // 输入参数
  output Json? // 输出结果
  error  String? // 错误信息

  // 关联的资源
  novelId   String?
  chapterId String?

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  completedAt DateTime?

  @@map("processing_tasks")
}

// 任务类型枚举
enum TaskType {
  NOVEL_ANALYSIS // 小说分析
  CHAPTER_ANALYSIS // 章节分析
  CHAPTER_MODIFY // 章节修改
  CONSISTENCY_CHECK // 一致性检查
}

// 任务状态枚举
enum TaskStatus {
  PENDING // 等待中
  RUNNING // 运行中
  COMPLETED // 已完成
  FAILED // 失败
  CANCELLED // 已取消
}
