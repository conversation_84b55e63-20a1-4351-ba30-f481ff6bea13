import { Router } from 'express';

const router = Router();

// API 根路由
router.get('/', (req, res) => {
  res.json({
    message: '小说个性化重塑引擎 API',
    version: '1.0.0',
    endpoints: {
      projects: '/api/projects',
      novels: '/api/novels',
      chapters: '/api/chapters',
      ai: '/api/ai',
      health: '/health'
    },
    documentation: 'https://github.com/your-repo/novel-reshaper'
  });
});

export default router;
